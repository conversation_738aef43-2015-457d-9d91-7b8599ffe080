import type { RouteRecordRaw } from 'vue-router'

export default [
  {
    path: '/feedback/config',
    name: 'feedback-config',
    component: () => import('~/pages/feedback/config/index.vue'),
    meta: {},
  },
  {
    path: '/feedback/trigger',
    name: 'feedback-trigger',
    component: () => import('~/pages/feedback/trigger/index.vue'),
    meta: {},
  },
  {
    path: '/feedback/trigger-send',
    name: 'feedback-trigger-send',
    component: () => import('~/pages/feedback/trigger-send/index.vue'),
    meta: {},
  },
  {
    path: '/feedback/handle',
    name: 'feedback-handle',
    component: () => import('~/pages/feedback/handle/index.vue'),
    meta: {
      isPublic: true,
      noLayout: true,
    },
  },
] satisfies RouteRecordRaw[]
